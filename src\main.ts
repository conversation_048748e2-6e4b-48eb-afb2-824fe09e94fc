import { createApp } from 'vue'
//import './style.css'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPLusIconsVue from '@element-plus/icons-vue'
import './assets/css/global.scss'

const app = createApp(App)
app.use(ElementPlus)
for(const[key,component] of Object.entries(ElementPLusIconsVue)){
   app.component(key,component)
}

app.use(router).mount('#app')