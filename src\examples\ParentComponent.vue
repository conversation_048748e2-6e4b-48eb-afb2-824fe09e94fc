<template>
  <div class="parent-container">
    <h1>父组件</h1>
    <p>父组件的数据：{{ parentMessage }}</p>
    <p>从子组件接收到的数据：{{ childMessage }}</p>
    
    <!-- 使用子组件，通过 props 传递数据 -->
    <ChildComponent 
      :title="parentMessage"
      :count="parentCount"
      @update-message="handleChildMessage"
      @increment="handleIncrement"
    />
    
    <div class="controls">
      <button @click="changeParentMessage">改变父组件消息</button>
      <button @click="resetCount">重置计数</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChildComponent from './ChildComponent.vue'

// 父组件的数据
const parentMessage = ref('来自父组件的消息')
const parentCount = ref(0)
const childMessage = ref('')

// 处理子组件发送的消息
const handleChildMessage = (message: string) => {
  childMessage.value = message
}

// 处理子组件的计数事件
const handleIncrement = (newCount: number) => {
  parentCount.value = newCount
}

// 父组件的方法
const changeParentMessage = () => {
  parentMessage.value = `更新时间：${new Date().toLocaleTimeString()}`
}

const resetCount = () => {
  parentCount.value = 0
}
</script>

<style scoped>
.parent-container {
  padding: 20px;
  border: 2px solid #409eff;
  border-radius: 8px;
  margin: 20px;
  background-color: #f0f9ff;
}

.controls {
  margin-top: 20px;
}

.controls button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.controls button:hover {
  background: #337ecc;
}
</style>
