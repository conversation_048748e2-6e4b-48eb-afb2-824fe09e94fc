{"name": "dashu.vipp.web.ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.10.5", "pinia": "^2.3.1", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.2.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.89.2", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}