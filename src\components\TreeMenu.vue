<template>
    <template v-for="item in list" :key="item.index">
        <el-menu-item :index=item.Index v-if="!item.Children">
            <IconCom :icon="item.Icon"></IconCom>
            <template #title><span>{{ item.Name }}</span></template>
        </el-menu-item>
        <el-sub-menu :index=item.Index v-else>
            <template #title>
                <IconCom :icon="item.Icon"></IconCom>
                <span>{{ item.Name }}</span>
            </template>
            <!-- 递归调用组件 -->
            <TreeMenu :list="item.Children"></TreeMenu>
        </el-sub-menu>
    </template>
</template>
<script setup lang="ts">
import TreeMenu from '../class/TreeMenu';
import IconCom from './IconCom.vue';
defineProps({
    list: Array<TreeMenu>
})
</script>