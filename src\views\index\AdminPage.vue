<!-- <template>
    <el-container>
             <el-aside width="200px">Aside</el-aside>
        <el-container>
            <el-header>Header</el-header>
            <el-main><router-view></router-view></el-main>
        </el-container>
    </el-container>
</template> -->

<template>
    <div class="common-layout">
        <el-container>
      <el-aside style="width:inherit;">
        <el-menu
        active-text-color="#ffd04b"
        background-color="#545c64"
        class="el-menu-vertical-demo"
        router :collapse="isCollapse" :unique-opened="true"
        default-active="2"
        text-color="#fff"
      >
      <!-- 静态菜单 -->
        <el-sub-menu index="/desktop">
          <template #title>
            <IconCom icon="house"></IconCom>
            <span>我的主页</span>
          </template>
        <el-menu-item index="/desktop">
          <IconCom icon="wallet"></IconCom>
          <span>工作台</span>
        </el-menu-item>
        <el-menu-item index="/person">
          <IconCom icon="monitor"></IconCom>
          <span>个人信息</span>
        </el-menu-item>
        <!-- <el-menu-item index="/menu">
            <IconCom icon="wallet"></IconCom>
            <span>菜单列表</span>
         </el-menu-item>
         <el-menu-item index="/role">
            <IconCom icon="wallet"></IconCom>
            <span>角色列表</span>
         </el-menu-item>
            <el-menu-item index="/user">
            <IconCom icon="wallet"></IconCom>
            <span>用户列表</span>
         </el-menu-item> -->
        </el-sub-menu>
        <!-- 动态菜单 -->
         <TreeMenu :list="treelist"></TreeMenu>
      </el-menu>
      </el-aside>
      <el-container>
        <el-header style="height: initial;">
            <HeaderCom></HeaderCom>
        </el-header>
        <el-main><router-view></router-view></el-main>
      </el-container>
    </el-container>
    </div>
</template>
<script setup lang="ts">
// import { computed } from 'vue';
// import HeaderCom from '../../components/HeaderCom.vue';
// import IconCom from '../../components/IconCom.vue';
// import TreeMenu from '../../components/TreeMenu.vue';
// import useStore from '../../store/index';
// const treelist = computed(() => useStore().UserMenus)
// const isCollapse = computed(() => useStore().isCollapse)
</script>
