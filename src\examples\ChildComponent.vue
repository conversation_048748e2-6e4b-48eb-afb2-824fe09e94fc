<template>
  <div class="child-container">
    <h2>子组件</h2>
    
    <!-- 显示从父组件接收的数据 -->
    <p>从父组件接收的标题：{{ title }}</p>
    <p>从父组件接收的计数：{{ count }}</p>
    
    <!-- 子组件自己的数据 -->
    <p>子组件的消息：{{ childOwnMessage }}</p>
    
    <div class="child-controls">
      <input 
        v-model="childOwnMessage" 
        placeholder="输入消息发送给父组件"
        @keyup.enter="sendMessageToParent"
      />
      <button @click="sendMessageToParent">发送消息给父组件</button>
      <button @click="incrementCount">增加计数</button>
    </div>
    
    <!-- 插槽示例 -->
    <div class="slot-area">
      <h3>插槽内容：</h3>
      <slot name="header">默认头部内容</slot>
      <slot>默认内容</slot>
      <slot name="footer">默认底部内容</slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 定义 props（从父组件接收的数据）
interface Props {
  title: string
  count: number
}

const props = defineProps<Props>()

// 定义 emits（向父组件发送的事件）
const emit = defineEmits<{
  'update-message': [message: string]
  'increment': [count: number]
}>()

// 子组件自己的数据
const childOwnMessage = ref('子组件的初始消息')

// 子组件的方法
const sendMessageToParent = () => {
  // 向父组件发送事件
  emit('update-message', childOwnMessage.value)
}

const incrementCount = () => {
  // 向父组件发送新的计数值
  emit('increment', props.count + 1)
}
</script>

<style scoped>
.child-container {
  padding: 15px;
  border: 2px solid #67c23a;
  border-radius: 8px;
  margin: 15px 0;
  background-color: #f0f9f0;
}

.child-controls {
  margin: 15px 0;
}

.child-controls input {
  padding: 8px;
  margin-right: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

.child-controls button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #67c23a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.child-controls button:hover {
  background: #5daf34;
}

.slot-area {
  margin-top: 15px;
  padding: 10px;
  background: #fff;
  border-radius: 4px;
  border: 1px dashed #ccc;
}
</style>
