import { defineStore } from "pinia";
import TagModel from "../class/TagModel";
import TreeMenu from "../components/TreeMenu.vue";

const useStore = defineStore('main', {
    state: () => {
        return {
            Token:"",
            isCollapse:false,
            //所有这些属性都将自动推断出类型
            tags:[] as TagModel[],
            //token刷新次数
            RefreshTokenNum:0
        }
    },
    //状态管理
    persist:{
       //开启
       enabled:true,
       strategies:[
           {
               //存储的key
               key:"site",
               //存储的位置
               storage:localStorage
           }
       ]
    }
})

export default useStore