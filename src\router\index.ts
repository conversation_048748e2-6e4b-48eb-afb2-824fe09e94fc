import { createRouter, createWebHistory } from "vue-router";
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: "login",
      path: "/login",
      component: () => import("../views/index/LoginPage.vue"),
    },
    {
      name: "notfound",
      path: "/:pathMatch(.*)",
      component: () => import("../views/index/NotFound.vue"),
    },
    {
      name: "admin",
      path: "/admin",
      component: () => import("../views/index/AdminPage.vue"),
      children: [
        {
          name: "desktop",
          path: "/desktop",
          component: () => import("../views/index/Desktop.vue"),
        },
        {
          name: "person",
          path: "/person",
          component: () => import("../views/index/PersonPage.vue"),
        },
        {
          name: "user",
          path: "/user",
          component: () => import("../views/admin/user/user.vue"),
        },
        {
          name: "role",
          path: "/role",
          component: () => import("../views/admin/role/role.vue"),
        },
        {
          name: "menu",
          path: "/menu",
          component: () => import("../views/admin/menu/menu.vue"),
        },
      ],
    },
  ],
});

export default router;
