<template>
    <el-row>
        <el-col :span="1">
            <el-link :underline="false" @click="ChangeisCollapse">
                <el-icon>
                    <Expand />
                </el-icon>
            </el-link>
        </el-col>
        <el-col :span="11">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><a href="/">
                        <el-icon class="middle">
                            <house />
                        </el-icon>
                        <span class="middle">首页</span>
                    </a>
                </el-breadcrumb-item>
                <el-breadcrumb-item>
                    <span class="middle">{{ router.currentRoute.value.name }}</span>
                </el-breadcrumb-item>
            </el-breadcrumb>
        </el-col>
        <el-col :span="12">
            <div class="dropdown">
                <el-avatar :size="30" :src="circleUrl" />
                <el-dropdown>
                    <span>
                        {{ NickName }}
                        <el-icon>
                            <arrow-down />
                        </el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item>我的主页</el-dropdown-item>
                            <el-dropdown-item @click="logOut">退出</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </el-col>
    </el-row>
    <el-row>
        <el-col :span="24">
            <el-divider />
            <div>
                <el-tag v-for="item in tags" :key="item.Index" closable class="ml-2"
                    :effect="item.Checked ? 'dark' : 'plain'" @close="handleClose(item.Index)"
                    @click="tagClick(item.Index)">{{ item.Name }}</el-tag>
            </div>
        </el-col>
    </el-row>
</template>
<script setup lang="ts">
import { ref,onMounted } from 'vue';
import router from '../router';
import store from '../store/index'
import { FormatToken,handleSelect,tagClick } from '../tools'
const circleUrl = ref(FormatToken(store().Token)?.Image)
const NickName = ref(FormatToken(store().Token)?.NickName)
//从全局状态中读取tags
const tags = ref(store().tags)
onMounted(() => {
    //读取路由
    let index = router.currentRoute.value.path    if (!tags.value.find(p => p.Checked)) {
        //设置tags
        handleSelect(index)
        //点击tag
        tagClick(index)
    } else {
        //点击tag
        tagClick(index)
    }
})
const handleClose = (index:string) => {
    tags.value = tags.value.filter(p => p.Index != index)
    store().$patch({
        tags: tags.value
    })
}
const ChangeisCollapse = () => {
    store().$patch({
        isCollapse: !store().isCollapse
    })
}
const logOut = () => {
    store().reset()
    router.push({ path: "/login" })
}
</script>
<style lang="scss" scoped>
.el-header {
    .el-col {
        height: 50px;
        line-height: 50px;
   
        .el-breadcrumb {
            line-height: inherit;
        }

        .el-icon {
            margin-right: 5px;
        }

        .el-divider {
            margin: 0;
        }
    }
}

.dropdown {
    float: right;
    height: 50px;
    line-height: 70px;
}

.el-dropdown {
    margin-top: 15px;
    margin-left: 5px;
}

.ml-2 {
    // 彼此之间间隔一二
    margin-left: 10px;
    // 设置鼠标手样式
    cursor: pointer;
}
</style>