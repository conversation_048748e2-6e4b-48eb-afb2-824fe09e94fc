<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧装饰区域 -->
      <div class="login-left">
        <div class="welcome-content">
          <h1 class="welcome-title">欢迎回来</h1>
          <p class="welcome-subtitle">登录您的账户以继续使用我们的服务</p>
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-right">
        <div class="login-form-container">
          <div class="login-header">
            <h2 class="login-title">登录</h2>
            <p class="login-subtitle">请输入您的账户信息</p>
          </div>

          <el-form class="login-form" :model="form" :rules="rules" ref="loginForm" label-position="top">
            <el-form-item label="用户名" prop="userName" class="form-item">
              <el-input
                v-model="form.userName"
                placeholder="请输入用户名"
                size="large"
                prefix-icon="User"
                class="custom-input"
              />
            </el-form-item>

            <el-form-item label="密码" prop="passWord" class="form-item">
              <el-input
                v-model="form.passWord"
                type="password"
                placeholder="请输入密码"
                size="large"
                prefix-icon="Lock"
                show-password
                class="custom-input"
                @keyup.enter="onSubmit()"
              />
            </el-form-item>

            <div class="form-options">
              <el-checkbox>记住我</el-checkbox>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <el-form-item class="form-buttons">
              <el-button
                type="primary"
                size="large"
                class="login-btn"
                @click="onSubmit(loginForm)"
              >
                登录
              </el-button>
              <el-button
                size="large"
                class="reset-btn"
                @click="reset(loginForm)"
              >
                重置
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <p>还没有账户？ <a href="#" class="register-link">立即注册</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive,ref } from 'vue';
import { ElMessage,FormInstance,FormRules } from 'element-plus';
import router from '../../router';
import store from '../../store/index'
import { getToken } from '../../http';

 const form = reactive({
    userName:'',
    passWord:''
 })

const loginForm = ref<FormInstance>()
const rules = reactive<FormRules>({
    userName:[
        {
          required:true,
          message:'请输入用户名',
          trigger:'blur'
        }
    ],
    passWord:[
        {
          required:true,
          message:'请输入密码',
          trigger:'blur'
        }
    ]
})

 //实现简单的表单逻辑验证
 const onSubmit = (loginForm : FormInstance | undefined) => {
    if(!loginForm) return
    loginForm.validate((valid:boolean,fields:any) => {
       if(valid){
         ElMessage.success('登录成功')

       }else{
        ElMessage.error('验证失败')
        console.log(fields);
       }
    })
    console.log(form)
 }

 const reset = (loginForm : FormInstance | undefined) => {
    if(!loginForm)return;
    loginForm.resetFields()
 }
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/public/bg.png');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 0;
  }
}

.login-wrapper {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 900px;
  width: 100%;
  min-height: 500px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 40px;

  .welcome-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
  }

  .welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .welcome-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.6;
    margin: 0;
  }

  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);

    &.circle-1 {
      width: 200px;
      height: 200px;
      top: -100px;
      right: -100px;
      animation: float 6s ease-in-out infinite;
    }

    &.circle-2 {
      width: 150px;
      height: 150px;
      bottom: -75px;
      left: -75px;
      animation: float 8s ease-in-out infinite reverse;
    }

    &.circle-3 {
      width: 100px;
      height: 100px;
      top: 50%;
      left: 20px;
      animation: float 10s ease-in-out infinite;
    }
  }
}

.login-right {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;

  .login-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .login-subtitle {
    color: #7f8c8d;
    font-size: 0.95rem;
    margin: 0;
  }
}

.login-form {
  .form-item {
    margin-bottom: 24px;

    :deep(.el-form-item__label) {
      color: #2c3e50;
      font-weight: 500;
      font-size: 0.9rem;
      margin-bottom: 8px;
    }
  }

  .custom-input {
    :deep(.el-input__wrapper) {
      border-radius: 12px;
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        border-color: #667eea;
      }

      &.is-focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    :deep(.el-input__inner) {
      padding: 12px 16px;
      font-size: 0.95rem;
    }

    :deep(.el-input__prefix-inner) {
      color: #7f8c8d;
    }
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;

    &:hover {
      color: #764ba2;
    }
  }
}

.form-buttons {
  margin-bottom: 0;

  :deep(.el-form-item__content) {
    display: flex;
    gap: 12px;
  }

  .login-btn {
    flex: 2;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }
  }

  .reset-btn {
    flex: 1;
    height: 48px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    color: #7f8c8d;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
      color: #667eea;
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 24px;

  p {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
  }

  .register-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;

    &:hover {
      color: #764ba2;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    margin: 20px;
  }

  .login-left {
    min-height: 200px;
    padding: 30px 20px;

    .welcome-title {
      font-size: 2rem;
    }

    .welcome-subtitle {
      font-size: 1rem;
    }
  }

  .login-right {
    padding: 30px 20px;
  }

  .form-buttons {
    :deep(.el-form-item__content) {
      flex-direction: column;
    }

    .login-btn,
    .reset-btn {
      flex: none;
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-wrapper {
    margin: 10px;
  }

  .login-left,
  .login-right {
    padding: 20px 15px;
  }
}
</style>