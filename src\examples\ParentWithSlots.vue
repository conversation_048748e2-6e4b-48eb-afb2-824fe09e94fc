<template>
  <div class="parent-with-slots">
    <h1>使用插槽的父组件</h1>
    
    <ChildComponent 
      :title="message"
      :count="count"
      @update-message="handleMessage"
      @increment="handleIncrement"
    >
      <!-- 具名插槽 -->
      <template #header>
        <div style="color: blue; font-weight: bold;">
          🎉 自定义头部内容
        </div>
      </template>
      
      <!-- 默认插槽 -->
      <div style="color: green;">
        <p>这是通过插槽传递的自定义内容</p>
        <ul>
          <li>插槽可以传递任何内容</li>
          <li>包括 HTML、组件、数据等</li>
        </ul>
      </div>
      
      <!-- 具名插槽 -->
      <template #footer>
        <div style="color: red; font-style: italic;">
          📝 自定义底部内容 - {{ new Date().toLocaleDateString() }}
        </div>
      </template>
    </ChildComponent>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ChildComponent from './ChildComponent.vue'

const message = ref('插槽示例消息')
const count = ref(10)

const handleMessage = (msg: string) => {
  console.log('收到子组件消息:', msg)
}

const handleIncrement = (newCount: number) => {
  count.value = newCount
}
</script>

<style scoped>
.parent-with-slots {
  padding: 20px;
  border: 2px solid #e6a23c;
  border-radius: 8px;
  margin: 20px;
  background-color: #fdf6ec;
}
</style>
