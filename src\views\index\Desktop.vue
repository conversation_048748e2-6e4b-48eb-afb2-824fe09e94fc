<template>
    <div>
        desktop
    </div>
    <div>
        <IconCom icon="Edit"></IconCom>
        <IconCom icon="Message"></IconCom>
    </div>
    <!-- <div>
        <el-icon><ChatLineRound /></el-icon>
    </div>
    <div>
        <el-button type="primary" :icon="Edit" />
    </div> -->
</template>
<script setup lang="ts">
 //import { Edit } from '@element-plus/icons-vue';
 import IconCom from '../../components/IconCom.vue'

</script>